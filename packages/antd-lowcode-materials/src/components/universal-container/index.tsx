import React from 'react';
import './index.scss';

import { UniversalContainerProps } from './types';

const UniversalContainer = (props: UniversalContainerProps) => {
  const {
    title,
    icon,
    content,
    style,
  } = props;

  return (
    <div className="universal-container" style={style}>
        <div className="header">
          <div
            className="header-title"
          >
            <img src='https://pcdemo.finebi.com/webroot/decision/v5/design/image/holder1721895148691_Iq5KPybfbo' className='header-icon' />
            {title}
          </div>
        </div>
      {content}
      
    </div>
  )
};

export default UniversalContainer;
