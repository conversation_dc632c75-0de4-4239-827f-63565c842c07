import snippets from './snippets';

export default {
  snippets,
  componentName: 'BarChart',
  group: 'MelGeek组件',
  title: '条形图',
  category: '驾驶舱',
  props: [
    {
      name: 'data',
      title: { label: '图表数据', tip: '条形图的数据源，数组格式' },
      propType: 'array',
      setter: 'JsonSetter',
      defaultValue: [
        { category: '类别A', value: 40 },
        { category: '类别B', value: 30 },
        { category: '类别C', value: 50 },
        { category: '类别D', value: 35 },
      ],
    },
    {
      name: 'xField',
      title: { label: 'X轴字段', tip: 'X轴对应的数据字段名' },
      propType: 'string',
      setter: 'StringSetter',
      defaultValue: 'value',
    },
    {
      name: 'yField',
      title: { label: 'Y轴字段', tip: 'Y轴对应的数据字段名' },
      propType: 'string',
      setter: 'StringSetter',
      defaultValue: 'category',
    },
    {
      name: 'color',
      title: { label: '条形颜色', tip: '条形图的颜色' },
      propType: 'string',
      setter: 'ColorSetter',
      defaultValue: '#1890ff',
    },
    {
      name: 'height',
      title: { label: '图表高度', tip: '图表的高度，单位为像素' },
      propType: 'number',
      setter: 'NumberSetter',
      defaultValue: 400,
    },
    {
      name: 'showLabel',
      title: { label: '显示标签', tip: '是否在条形上显示数值标签' },
      propType: 'bool',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'labelPosition',
      title: { label: '标签位置', tip: '标签在条形上的位置' },
      propType: 'oneOf',
      setter: {
        componentName: 'SelectSetter',
        props: {
          options: [
            { title: '左侧', value: 'left' },
            { title: '中间', value: 'middle' },
            { title: '右侧', value: 'right' },
          ],
        },
      },
      defaultValue: 'right',
      condition: (target: any) => {
        return !!target.getProps().getPropValue('showLabel');
      },
    },
    {
      name: 'sort',
      title: { label: '排序', tip: '是否对数据进行排序' },
      propType: 'bool',
      setter: 'BoolSetter',
      defaultValue: false,
    },
    {
      name: 'sortReverse',
      title: { label: '倒序', tip: '是否倒序排列' },
      propType: 'bool',
      setter: 'BoolSetter',
      defaultValue: false,
      condition: (target: any) => {
        return !!target.getProps().getPropValue('sort');
      },
    },
    {
      name: 'marginRatio',
      title: { label: '条形间距', tip: '条形之间的间距比例，0-1之间' },
      propType: 'number',
      setter: {
        componentName: 'NumberSetter',
        props: {
          min: 0,
          max: 1,
          step: 0.1,
        },
      },
      defaultValue: 0.3,
    },
    {
      name: 'showGrid',
      title: { label: '显示网格', tip: '是否显示网格线' },
      propType: 'bool',
      setter: 'BoolSetter',
      defaultValue: true,
    },
  ],
};
