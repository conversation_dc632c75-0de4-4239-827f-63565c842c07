export default [
  {
    name: 'title',
    title: '容器标题',
    defaultValue: '容器标题',
    setter: 'StringSetter',
  },
  {
    name: 'content',
    title: '容器内容',
    setter: 'SlotSetter',
  },
  {
    name: 'style',
    type: 'group',
    display: 'accordion',
    title: {
      label: '样式',
    },
    items: [
      {
        name: 'style',
        display: 'line',
        setter: 'StyleSetter',
      },
    ],
  },
  // To Be Continue
  // {
  //   name: 'foldSettings',
  //   type: 'group',
  //   display: 'accordion',
  //   title: {
  //     label: '组件折叠',
  //   },
  //   items: [
  //     {
  //       name: 'foldContent',
  //       title: '折叠功能',
  //       setter: {
  //         componentName: 'SlotSetter',
  //         isRequired: true,
  //         title: '折叠组件坑位',
  //         initialValue: {
  //           type: 'JSSlot',
  //           value: [],
  //         },
  //       }
  //     },
  //     {
  //       name: 'foldInitStatus',
  //       title: '初始状态',
  //       defaultValue: true,
  //       condition: (target: any) => {
  //         return !!target.getProps().getPropValue('foldContent');
  //       },
  //       setter: {
  //         componentName: 'RadioGroupSetter',
  //         props: {
  //           options: [
  //             {
  //               title: '收起',
  //               value: true,
  //             },
  //             {
  //               title: '展开',
  //               value: false,
  //             },
  //           ],
  //         },
  //       },
  //     }]
  // },
];
